This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.6.5)  5 JUN 2024 11:58
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./agujournaltemplate.tex
(agujournaltemplate.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-04-11>
(agujournal2019.cls
Document Class: agujournal2019 01/14/2019

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen140
\Gin@req@width=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lineno\lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count189
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
\linenopenalty=\count190
\output=\toks18
\linenoprevgraf=\count191
\linenumbersep=\dimen142
\linenumberwidth=\dimen143
\c@linenumber=\count192
\c@pagewiselinenumber=\count193
\c@LN@truepage=\count194
\c@internallinenumber=\count195
\c@internallinenumbers=\count196
\quotelinenumbersep=\dimen144
\bframerule=\dimen145
\bframesep=\dimen146
\bframebox=\box51
LaTeX Info: Redefining \\ on input line 3180.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\indentfirst.s
ty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/ragged2e\ragged2e.s
ty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip48
\RaggedLeftLeftskip=\skip49
\RaggedRightLeftskip=\skip50
\CenteringRightskip=\skip51
\RaggedLeftRightskip=\skip52
\RaggedRightRightskip=\skip53
\CenteringParfillskip=\skip54
\RaggedLeftParfillskip=\skip55
\RaggedRightParfillskip=\skip56
\JustifyingParfillskip=\skip57
\CenteringParindent=\skip58
\RaggedLeftParindent=\skip59
\RaggedRightParindent=\skip60
\JustifyingParindent=\skip61
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\rotating.s
ty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\c@r@tfl@t=\count197
\rotFPtop=\skip62
\rotFPbot=\skip63
\rot@float@box=\box52
\rot@mess@toks=\toks19
)
\draftskip=\count198
\c@section=\count199
\c@subsection=\count266
\c@subsubsection=\count267
\c@paragraph=\count268
\c@subparagraph=\count269
\c@figure=\count270
\c@table=\count271
\abovecaptionskip=\skip64
\belowcaptionskip=\skip65
\c@appendnum=\count272
\bibindent=\dimen147

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/apacite\apacite.sty
Package: apacite 2013/07/21 v6.03 APA citation
\c@BibCnt=\count273
\bibleftmargin=\skip66
\bibindent=\skip67
\bibparsep=\skip68
\bibitemsep=\skip69
\biblabelsep=\skip70
)) (trackchanges.sty
Package: trackchanges 2009/04/22 v0.7.0 TrackChanges file 

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count274
\calc@Bcount=\count275
\calc@Adimen=\dimen148
\calc@Bdimen=\dimen149
\calc@Askip=\skip71
\calc@Bskip=\skip72
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count276
\calc@Cskip=\skip73
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/soul\soul.sty
Package: soul 2023-06-14 v3.1 Permit use of UTF-8 characters in soul (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/soul\soul-ori.sty
Package: soul-ori 2023-06-14 v3.1 letterspacing/underlining (mf)
\SOUL@word=\toks20
\SOUL@lasttoken=\toks21
\SOUL@syllable=\toks22
\SOUL@cmds=\toks23
\SOUL@buffer=\toks24
\SOUL@token=\toks25
\SOUL@syllgoal=\dimen150
\SOUL@syllwidth=\dimen151
\SOUL@charkern=\dimen152
\SOUL@hyphkern=\dimen153
\SOUL@dimen=\dimen154
\SOUL@dimeni=\dimen155
\SOUL@minus=\count277
\SOUL@comma=\count278
\SOUL@apo=\count279
\SOUL@grave=\count280
\SOUL@spaceskip=\skip74
\SOUL@ttwidth=\dimen156
\SOUL@uldp=\dimen157
\SOUL@ulht=\dimen158
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infware
rr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/etexcmds\etexcmds
.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/morefloats\morefloa
ts.sty
Package: morefloats 2015/07/22 v1.0h Raise limit of unprocessed floats (HMM)
Package morefloats Info: Maximum number of possible floats asked for: 36
(morefloats)             (i.e. 18 more floats).
(morefloats)             LaTeX might run out of memory before this
(morefloats)             (in which case it will notify you).
)
\c@userid=\count281
\c@changenumber=\count282
\c@storefootnote=\count283
\c@numeditors=\count284
\c@maxeditors=\count285
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-04-11 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count286
\l__pdf_internal_box=\box53
)
No file agujournaltemplate.aux.
\openout1 = `agujournaltemplate.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 64.
LaTeX Font Info:    ... okay on input line 64.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count287
\scratchdimen=\dimen159
\scratchbox=\box54
\nofMPsegments=\count288
\nofMParguments=\count289
\everyMPshowfont=\toks26
\MPscratchCnt=\count290
\MPscratchDim=\dimen160
\MPnumerator=\count291
\makeMPintoPDFobject=\count292
\everyMPtoPDFconversion=\toks27
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/grfext\grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvde
finekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftex
cmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPE
G,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
\c@maskedRefs=\count293

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/apacite\english.apc
File: english.apc 2013/07/21 v6.03 apacite language file
LaTeX Info: Redefining \BPBI on input line 129.
LaTeX Info: Redefining \BHBI on input line 130.
)
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 107.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 107.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 107.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 138.
 [1{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]

LaTeX Font Warning: Font shape `OT1/cmr/m/n' in size <10.5> not available
(Font)              size <10.95> substituted on input line 169.


LaTeX Font Warning: Font shape `OT1/cmr/bx/n' in size <10.5> not available
(Font)              size <10.95> substituted on input line 169.


Overfull \hbox (11.04338pt too wide) in paragraph at lines 170--173
[]\OT1/cmr/m/n/10 Enter your Plain Lan-guage Sum-mary here or delete this sec-t
ion. Here are in-struc- 
 []


Overfull \hbox (130.79395pt too wide) in paragraph at lines 170--173
\OT1/cmr/m/n/10 tions on writ-ing a Plain Lan-guage Sum-mary: https://www.agu.o
rg/Share-and-Advocate/Share/Community/Plain- 
 []


Overfull \hbox (9.82117pt too wide) in paragraph at lines 373--375
[]\OT1/cmr/m/n/10 This sec-tion MUST con-tain a state-ment that de-scribes wher
e the data sup-port-ing 
 []


Overfull \hbox (5.90463pt too wide) in paragraph at lines 373--375
\OT1/cmr/m/n/10 the con-clu-sions can be ob-tained. Data can-not be listed as '
'Avail-able from au-thors'' or 
 []
