This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: OH_Ep_Manuscript_AGU_Corr_New.aux
Reallocating 'name_of_file' (item size: 1) to 8 items.
The style file: apacite.bst
Reallocating 'name_of_file' (item size: 1) to 11 items.
Reallocating 'glb_str_end' (item size: 4) to 20 items.
Reallocating 'glb_str_ptr' (item size: 4) to 20 items.
Reallocating 'global_strs' (item size: 1) to 4000000 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Reallocating 'wiz_functions' (item size: 4) to 9000 items.
Reallocating 'singl_function' (item size: 4) to 100 items.
Database file #1: references.bib
Repeated entry---line 548 of file references.bib
 : @article{Fritts2003
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 559 of file references.bib
 : @article{Fytterer2015
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 647 of file references.bib
 : @article{Nielsen2012
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 680 of file references.bib
 : @article{Sato2009
 :                  ,
I'm skipping whatever remains of this entry
Repeated entry---line 759 of file references.bib
 : @article{Vincent2013
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 840 of file references.bib
 : @article{Garcia2014
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 851 of file references.bib
 : @article{Offermann2010
 :                       ,
I'm skipping whatever remains of this entry
Repeated entry---line 872 of file references.bib
 : @article{Liu2017
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 984 of file references.bib
 : @article{Smith2010
 :                   ,
I'm skipping whatever remains of this entry
Repeated entry---line 1094 of file references.bib
 : @article{Yue2019
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 1147 of file references.bib
 : @article{Fritts2016
 :                    ,
I'm skipping whatever remains of this entry
apacite.bst [2013/07/21 v6.03 APA bibliography style]
You've used 70 entries,
            6951 wiz_defined-function locations,
            1802 strings with 27595 characters,
and the built_in function-call counts, 172987 in all, are:
= -- 20672
> -- 1578
< -- 9300
+ -- 10671
- -- 141
* -- 12178
:= -- 32799
add.period$ -- 264
call.type$ -- 70
change.case$ -- 1190
chr.to.int$ -- 0
cite$ -- 140
duplicate$ -- 5391
empty$ -- 5847
format.name$ -- 3038
if$ -- 30806
int.to.chr$ -- 0
int.to.str$ -- 210
missing$ -- 0
newline$ -- 2053
num.names$ -- 560
pop$ -- 2150
preamble$ -- 1
purify$ -- 280
quote$ -- 0
skip$ -- 9727
stack$ -- 0
substring$ -- 11509
swap$ -- 2952
text.length$ -- 140
text.prefix$ -- 0
top$ -- 1
type$ -- 5600
warning$ -- 0
while$ -- 999
width$ -- 0
write$ -- 2720
(There were 11 error messages)
