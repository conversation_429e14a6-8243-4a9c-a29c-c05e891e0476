# Makefile for LaTeX manuscript compilation

# Main document
MAIN = main
TEXFILE = $(MAIN).tex
PDFFILE = $(MAIN).pdf
BIBFILE = references.bib

# LaTeX compiler
LATEX = pdflatex
BIBTEX = bibtex

# Compilation flags
LATEXFLAGS = -interaction=nonstopmode -halt-on-error

.PHONY: all clean distclean help

# Default target
all: $(PDFFILE)

# Main compilation rule
$(PDFFILE): $(TEXFILE) $(BIBFILE)
	$(LATEX) $(LATEXFLAGS) $(MAIN)
	$(BIBTEX) $(MAIN)
	$(LATEX) $(LATEXFLAGS) $(MAIN)
	$(LATEX) $(LATEXFLAGS) $(MAIN)

# Quick compilation without bibliography
quick: $(TEXFILE)
	$(LATEX) $(LATEXFLAGS) $(MAIN)

# Clean auxiliary files
clean:
	rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz

# Clean all generated files including PDF
distclean: clean
	rm -f $(PDFFILE)

# Help target
help:
	@echo "Available targets:"
	@echo "  all       - Compile the full document with bibliography"
	@echo "  quick     - Quick compilation without bibliography"
	@echo "  clean     - Remove auxiliary files"
	@echo "  distclean - Remove all generated files including PDF"
	@echo "  help      - Show this help message"
