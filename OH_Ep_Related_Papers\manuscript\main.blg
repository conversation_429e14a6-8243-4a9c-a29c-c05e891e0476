This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
Reallocating 'name_of_file' (item size: 1) to 4 items.
The style file: agu.bst
Reallocating 'name_of_file' (item size: 1) to 11 items.
Database file #1: references.bib
Repeated entry---line 182 of file references.bib
 : @article{Nikoukar2007
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 202 of file references.bib
 : @article{Russell1999
 :                     ,
I'm skipping whatever remains of this entry
Repeated entry---line 222 of file references.bib
 : @article{Schubert1991
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 233 of file references.bib
 : @article{She2019
 :                 ,
I'm skipping whatever remains of this entry
Repeated entry---line 244 of file references.bib
 : @article{Sivjee1987
 :                    ,
I'm skipping whatever remains of this entry
Repeated entry---line 288 of file references.bib
 : @article{Tarasick1992
 :                      ,
I'm skipping whatever remains of this entry
Repeated entry---line 321 of file references.bib
 : @article{Walterscheid1987
 :                          ,
I'm skipping whatever remains of this entry
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
You've used 24 entries,
            3075 wiz_defined-function locations,
            1228 strings with 15171 characters,
and the built_in function-call counts, 28584 in all, are:
= -- 3220
> -- 1270
< -- 5
+ -- 689
- -- 300
* -- 1799
:= -- 2486
add.period$ -- 24
call.type$ -- 24
change.case$ -- 169
chr.to.int$ -- 24
cite$ -- 72
duplicate$ -- 2901
empty$ -- 1391
format.name$ -- 382
if$ -- 5770
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 286
newline$ -- 79
num.names$ -- 96
pop$ -- 1382
preamble$ -- 1
purify$ -- 145
quote$ -- 0
skip$ -- 1801
stack$ -- 0
substring$ -- 1156
swap$ -- 2501
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 216
warning$ -- 0
while$ -- 123
width$ -- 0
write$ -- 270
(There were 7 error messages)
