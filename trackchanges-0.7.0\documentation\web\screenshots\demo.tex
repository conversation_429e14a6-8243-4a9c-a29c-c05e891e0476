\documentclass[a4paper,10pt]{article}

\usepackage{url}

\usepackage{trackchanges}
\renewcommand{\initialsOne}{FS}
\renewcommand{\initialsTwo}{PS}

\begin{document}

\title{Demonstration of trackchanges.sty}
\author{<PERSON>}

\maketitle

\begin{abstract} \noindent The intention of this text is to demonstrate the use of \texttt{trackchanges.sty}. The package defines five editing commands:
\begin{itemize}
  \item \begin{verbatim}\note[initials]{note text}\end{verbatim}
  \item \begin{verbatim}\annote[initials]{note text}\end{verbatim}
  \item \begin{verbatim}\add[initials]{additional text}\end{verbatim}
  \item \begin{verbatim}\remove[initials]{removed text}\end{verbatim}
  \item \begin{verbatim}\change[initials]{original text}{new text}\end{verbatim}
\end{itemize}
\end{abstract}

\section{Introduction} 

While planning to write some \add[FS]{complex} document together with some of my colleagues the same discussion was started over and over again: should \LaTeX\ or some office suite like \change[PS]{Microsoft Office}{OpenOffice.org} be used? Strengths of \LaTeX\ are its deterministic behavior, its reliable handling of split documents and unreached typesetting of formulas. On the other hand, current office suites provide the user with several features that are at least very desirable when collaboratively writing a document: they provide integrated merging facilities and are able to track changes and attach notes to the text. The merging issue can be reasonably handled by version tracking systems like SVN or CVS,\note[FS]{Should we emphasize that UNIX diff only works on line basis?} but there was no acceptable solution to the issue of change tracking available. Of course, some \remove[PS]{militant} \LaTeX\ purists tried to convince me that all change tracking can be handled by insertion of \LaTeX\ \textit{comments}. I have tried to handle one project like this but it did not work out! The main reason was that reading and editing of large documents is mostly handled in \annote[PS]{DVI}{what about PDF? The changes can be seen in PDF as well ...} format and not on the \LaTeX\ source level -- but \LaTeX\ comments cannot be seen in DVI! Especially, if you have sent one version of the document to a colleague and you want to skim quickly over it in order to see what has been changed.

While returning from a project meeting and staring out of the train's window I had the idea how we could combine the ``best of both worlds'' for collaborative text editing: by adding change tracking and note facilities to \LaTeX ! This is the basic idea of the \texttt{trackchanges} \LaTeX\ package. But this is only one part of the change tracking convenience offered by an office suite. The second part of the story is that changes and notes need to be accepted or rejected! This is the goal of the other programs of the \textbf{trackchanges} open source project hosted on sourceforge\footnote{Please visit  \url{http://trackchanges.sourceforge.net}}


\end{document}
%This document is part of the trackchanges sourceforge project and is published under GNU Public License version 2 or above
